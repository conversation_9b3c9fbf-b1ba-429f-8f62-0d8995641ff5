import React, { useEffect, useState } from 'react';
import { Image, StyleProp, ImageStyle } from 'react-native';

interface SalesforceImageProps {
  imageUrl: string;
  accessToken: string;
  style?: StyleProp<ImageStyle>;
}

const fallbackImageUrl =
  'https://i.pinimg.com/474x/15/0f/a8/150fa8800b0a0d5633abc1d1c4db3d87.jpg?nii=t';

const SalesforceImage: React.FC<SalesforceImageProps> = ({
  imageUrl,
  accessToken,
  style,
}) => {
  const [base64Image, setBase64Image] = useState<string | null>(null);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    const fetchImage = async () => {
      try {
        const response = await fetch(imageUrl, {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        });
        if (!response.ok) {
          throw new Error('Image fetch failed');
        }
        const blob = await response.blob();
        const reader = new FileReader();
        reader.onloadend = () => {
          setBase64Image(reader.result as string);
        };
        reader.readAsDataURL(blob);
      } catch (error) {
        console.warn('Failed to fetch Salesforce image:', error);
        setHasError(true);
      }
    };

    fetchImage();
  }, [imageUrl, accessToken]);

  if (!base64Image) return null;

  if (hasError) {
    return <Image source={{ uri: fallbackImageUrl }} style={style} />;
  }

  if (!base64Image) return null;

  return <Image source={{ uri: base64Image }} style={style} />;
};

export default SalesforceImage;
