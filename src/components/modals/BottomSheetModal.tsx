import React from 'react';
import { View, StyleSheet, Text, TouchableOpacity, TextStyle, ViewStyle } from 'react-native';
import Modal from 'react-native-modal';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import colors from '~/styles/colors';
import { buttonText, centerText } from '~/styles/text';
import { center, deviceWidth } from '~/styles/views';

/**
 * Interface for BottomSheetModal Props.
 */
interface BottomSheetModalProps {
  isVisible: boolean;
  onClose: () => void;
  children?: React.ReactNode;
  // New props for standardized button layout
  primaryActions?: Array<{
    title: string;
    onPress: () => void;
  }>;
  cancelAction?: {
    title: string;
    onPress: () => void;
  };
}

/**
 * The BottomSheetModal component renders a modal at the bottom of the screen with customizable options.
 * Supports both legacy children-based content and new standardized button layout.
 *
 * @component
 * @param {boolean} isVisible - Whether the modal is visible or not. Default is false.
 * @param {function} props.onClose - A callback function that is triggered when the modal is closed. Default is () => {}.
 * @param {React.ReactNode} props.children - Content displayed below the BlurView. Optional (legacy support).
 * @param {Array} props.primaryActions - Array of primary action buttons (connected at top). Optional.
 * @param {Object} props.cancelAction - Cancel button (independent at bottom with bold text). Optional.
 */
const BottomSheetModal = ({
  isVisible = false,
  onClose = () => {},
  children,
  primaryActions,
  cancelAction,
}: BottomSheetModalProps) => {
  const { bottom } = useSafeAreaInsets();

  const renderStandardizedContent = () => {
    if (!primaryActions && !cancelAction) return null;

    return (
      <View style={[styles.standardContainer, { paddingBottom: Math.max(bottom, 20) }]}>
        {/* Primary Actions - Connected buttons */}
        {primaryActions && primaryActions.length > 0 && (
          <View style={styles.primaryActionsContainer}>
            {primaryActions.map((action, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.primaryButton,
                  index === 0 && styles.firstPrimaryButton,
                  index === primaryActions.length - 1 && styles.lastPrimaryButton,
                ]}
                onPress={action.onPress}>
                <Text style={styles.primaryButtonText}>{action.title}</Text>
              </TouchableOpacity>
            ))}
          </View>
        )}

        {/* Cancel Action - Independent button */}
        {cancelAction && (
          <TouchableOpacity
            style={styles.cancelButton}
            onPress={cancelAction.onPress}>
            <Text style={styles.cancelButtonText}>{cancelAction.title}</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  return (
    <Modal
      isVisible={isVisible}
      onBackdropPress={onClose}
      onSwipeComplete={onClose}
      swipeDirection="down"
      style={styles.modal}
      backdropOpacity={0.5}
      backdropTransitionOutTiming={0}
      statusBarTranslucent>
      {/* Legacy support for children */}
      {children && <View>{children}</View>}

      {/* New standardized layout */}
      {renderStandardizedContent()}
    </Modal>
  );
};

const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  standardContainer: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    paddingHorizontal: 20,
    paddingTop: 20,
    gap: 16,
  } as ViewStyle,
  primaryActionsContainer: {
    backgroundColor: colors.white,
    borderRadius: 16,
    overflow: 'hidden',
  } as ViewStyle,
  primaryButton: {
    ...center,
    width: deviceWidth - 40,
    height: 65,
    backgroundColor: colors.grayishBlue,
    borderTopWidth: 1,
    borderTopColor: colors.lightGray,
  } as ViewStyle,
  firstPrimaryButton: {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    borderTopWidth: 0,
  } as ViewStyle,
  lastPrimaryButton: {
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
  } as ViewStyle,
  primaryButtonText: {
    ...buttonText,
    ...centerText,
    color: colors.blue600,
  } as TextStyle,
  cancelButton: {
    ...center,
    width: deviceWidth - 40,
    height: 65,
    borderRadius: 16,
    backgroundColor: colors.grayishBlue,
  } as ViewStyle,
  cancelButtonText: {
    ...buttonText,
    ...centerText,
    color: colors.blue600,
    fontWeight: 'bold',
  } as TextStyle,
});

export default BottomSheetModal;
