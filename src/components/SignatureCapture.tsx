/**
 * @file SignatureCapture.tsx
 * @description
 * A reusable component for capturing a digital signature using a canvas.
 * The signature is processed, compressed, and sent to the parent component via a callback.
 * Additional functionalities include clearing the signature and detecting the end of the drawing session.
 *
 * @component
 * @prop {function} isUserSigningCallback - Callback function that receives when the user starts and ends signing to enable/disable parent component scrolling.
 * @prop {function} onConfirmCallback - Callback function that receives the processed signature in base64 format.
 * @prop {string | undefined} [dataURL] - Preloaded base64 data for the signature (optional).
 *
 * @example
 * // Example usage:
 * import SignatureCapture from './SignatureCapture';
 *
 * <SignatureCapture
 *   isUserSigningCallback={(signature) => console.log(signature)}
 *   onConfirmCallback={(signature) => console.log(signature)}
 *   dataURL={preloadedSignature}
 * />
 */

import React, { useRef } from 'react';
import { StyleSheet, TouchableOpacity, View, ViewStyle } from 'react-native';
import SignatureScreen, {
  SignatureViewRef,
} from 'react-native-signature-canvas';
import { DustbinWithBackground } from '~/assets/icons';
import colors from '~/styles/colors';
import { container, deviceHeight, topRightContainer } from '~/styles/views';

// Props type definition for SignatureCapture component
type SignatureCapturePropsType = {
  /**
   * Callback function to send the processed signature in base64 format.
   */
  isUserSigningCallback?: (isSigning: boolean) => void;

  /**
   * Callback function to send the processed signature in base64 format.
   */
  onConfirmCallback: (sig: string) => void;

  /**
   * Optional preloaded signature data in base64 format.
   */
  signatureURI?: string;

  /**
   * Optional preloaded signature file path.
   */
};

/**
 * SignatureCapture Component
 * A functional component to provide a canvas for signature capture, handle the signature's lifecycle,
 * and return a compressed base64 image to the parent component via the provided callback.
 */
const SignatureCapture: React.FC<SignatureCapturePropsType> = ({
  isUserSigningCallback,
  onConfirmCallback,
  signatureURI,
}) => {
  // Reference to the SignatureScreen component
  const ref = useRef<SignatureViewRef>(null);

  // Custom CSS for the web implementation of the SignatureScreen
  const signatureWebStyle =
    '.m-signature-pad {border: none; box-shadow: none;} .m-signature-pad--footer {display: none;} .m-signature-pad--body {border: none;}';

  /**
   * Triggered when the user starts drawing on the canvas.
   */
  const handleBegin = () => {
    // Disable scrolling when drawing starts
    isUserSigningCallback?.(true);
  };

  /**
   * Triggered when the user stops drawing on the canvas.
   * Reads the signature data and processes it.
   */
  const handleEnd = () => {
    // Re-enable scrolling when drawing ends
    isUserSigningCallback?.(false);
    ref.current?.readSignature();
  };

  /**
   * Clears the signature from the canvas.
   */
  const handleClear = async () => {
    ref.current?.clearSignature();
    ref.current?.readSignature();
    onConfirmCallback('');
  };
  return (
    <View style={styles.container}>
      <SignatureScreen
        ref={ref}
        dataURL={signatureURI}
        onOK={onConfirmCallback}
        onBegin={handleBegin}
        onEnd={handleEnd}
        webStyle={signatureWebStyle}
        backgroundColor={colors.white}
      />
      <TouchableOpacity
        style={styles.dustbinIconContainer}
        onPress={handleClear}
        testID="dustbin-icon">
        <DustbinWithBackground width={48} height={48}/>
      </TouchableOpacity>
    </View>
  );
};

export default SignatureCapture;

/**
 * Styles for the SignatureCapture component.
 */
const styles = StyleSheet.create({
  container: {
    ...container,
    maxHeight: deviceHeight * 0.33,
    borderRadius: 12,
    overflow: 'hidden',
  },
  dustbinIconContainer: {
    ...topRightContainer,
    backgroundColor: colors.blue50,
    zIndex: 2,
    borderRadius: 40,
  } as ViewStyle,
});
