import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  Pressable,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { Checkmark } from '~/components/icons';
import colors from '~/styles/colors';
import { h3, grayishText } from '~/styles/text';
import { shadowSmall } from '~/styles/views';

// TODO: make custom styles optional
interface LanguageOptionProps {
  languageName: string;
  secondaryText?: string;
  flagUri: React.ReactNode;
  isSelected?: boolean;
  onPress?: () => void;
}

const LanguageOption = ({
  languageName,
  secondaryText,
  flagUri,
  isSelected = false,
  onPress,
}: LanguageOptionProps) => {
  return (
    <Pressable onPress={onPress} style={styles.card} testID="language-option">
      <Image
        source={flagUri as any}
        style={styles.flag}
        testID="flag-image"
      />
      <View style={styles.textContainer}>
        <Text style={styles.languageName}>{languageName}</Text>
        {secondaryText && (
          <Text style={styles.secondaryText}>{secondaryText}</Text>
        )}
      </View>
      <View style={styles.rightIcon}>
        {isSelected && <Checkmark color={colors.greenDark} size={32} />}
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  card: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    ...shadowSmall,
  } as ViewStyle,
  flag: {
    width: 33,
    height: 20,
    borderRadius: 2,
    marginRight: 16,
  },
  textContainer: {
    flex: 1,
    rowGap: 4,
  },
  languageName: {
    ...h3,
    fontSize: 16,
    color: colors.grey900,
  } as TextStyle,
  secondaryText: {
    ...grayishText,
    fontSize: 14,
    color: colors.grey600,
  } as TextStyle,
  rightIcon: {
    marginLeft: 12,
  },
});

export default LanguageOption;
