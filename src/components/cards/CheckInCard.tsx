import React, { useCallback, useEffect, useState } from 'react';
import { View, Text, StyleSheet, ViewStyle, TextStyle } from 'react-native';

import StopWatch from '~/components/icons/StopWatch';
import { useAppState } from '~/hooks/useAppState';
import { useTimer } from '~/hooks/useTimer';
import { formatTimeRemaining } from '~/utils/dateAndTime';

import { primarySolidButton } from '~/styles/buttons';
import colors from '~/styles/colors';
import FilledButton from '~/components/buttons/FilledButton';
import CardWrapper from '~/components/cards/CardWrapper';
import { h5, small } from '~/styles/text';
import { center, rowViewFillSpace } from '~/styles/views';
import { marginTop12 } from '~/styles/spacing';

type CheckInCardProps = {
  title: string;
  description: string;
  durationInMinutes?: number | null;
  isTimerActive?: boolean;
  startTimestamp?: number | null;
  isButtonEnabled: boolean;
  onPress: () => void;
  onComplete: () => void;
  icon?: React.ReactNode;
};

export const CheckInCard = ({
  title,
  description,
  durationInMinutes = null,
  isTimerActive = false,
  startTimestamp = null,
  isButtonEnabled,
  onPress,
  onComplete,
  icon,
}: CheckInCardProps) => {
  const totalSeconds = (durationInMinutes ?? 0) * 60;
  const [remainingSeconds, setRemainingSeconds] = useState<number | null>(null);
  const hasTimer = !!durationInMinutes && !!startTimestamp;

  const calculateRemaining = useCallback(() => {
    if (!startTimestamp) {
      return 0;
    }

    const elapsed = Math.floor((Date.now() - startTimestamp) / 1000);
    return totalSeconds - elapsed;
  }, [startTimestamp, totalSeconds]);

  const syncRemaining = useCallback(() => {
    if (!hasTimer || !isTimerActive) {
      return;
    }

    const remaining = calculateRemaining();

    if (remaining <= 0) {
      if (remainingSeconds !== 0) {
        setRemainingSeconds(0);
        // Use setTimeout to avoid calling onComplete during render
        setTimeout(() => onComplete(), 0);
      }
    } else {
      setRemainingSeconds(remaining);
    }
  }, [
    hasTimer,
    isTimerActive,
    calculateRemaining,
    remainingSeconds,
    onComplete,
  ]);

  useEffect(() => {
    if (remainingSeconds === null) {
      syncRemaining();
    }
  }, [syncRemaining, remainingSeconds]);

  // Sync on app foreground
  useAppState({
    onForeground: () => {
      syncRemaining();
    },
  });

  // Tick the timer every second
  useTimer({
    isRunning:
      isTimerActive && remainingSeconds !== null && remainingSeconds > 0,
    onTick: () => {
      setRemainingSeconds(prev => {
        if (prev === null || prev <= 1) {
          if (prev !== 0) {
            // Use setTimeout to avoid calling onComplete during render
            setTimeout(() => onComplete(), 0);
          }
          return 0;
        }

        return prev - 1;
      });
    },
  });

  const shouldShowTimer =
    hasTimer &&
    isTimerActive &&
    remainingSeconds !== null &&
    remainingSeconds > 0;

  return (
    <CardWrapper withShadow>
      <View style={rowViewFillSpace as ViewStyle}>
        <View style={styles.textContainer}>
          <View style={styles.titleRow}>
            <Text style={[h5 as TextStyle, styles.title]}>{title}</Text>
            {icon}
          </View>
          <Text style={styles.description}>{description}</Text>
        </View>

        {shouldShowTimer && (
          <View style={styles.timerBox}>
            <StopWatch color={colors.darkBlue900} />
            <Text style={styles.timeText}>
              {formatTimeRemaining(remainingSeconds)}
            </Text>
          </View>
        )}
      </View>

      <FilledButton
        title="Complete"
        onClick={onPress}
        isDisabled={hasTimer && !isButtonEnabled}
        id={'SurveyCheckInCard.Button.Complete'}
        containerStyle={{ flexGrow: 1 } as ViewStyle}
        color={'primary'}
        style={primarySolidButton}
      />
    </CardWrapper>
  );
};

const styles = StyleSheet.create({
  textContainer: {
    flex: 1,
  },
  description: {
    fontSize: small.fontSize,
    color: colors.darkGray,
    marginTop: marginTop12.marginTop,
    marginBottom: 4,
  },
  timeText: {
    fontSize: 16,
    color: colors.darkBlue600,
    fontWeight: '600',
    paddingTop: 8,
  } as TextStyle,
  timerBox: {
    ...center,
    backgroundColor: colors.blue50,
    justifyContent: 'space-evenly',
    borderRadius: 8,
    padding: 8,
  } as ViewStyle,
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: 6,
  } as ViewStyle,
  title: {
    flex: 1,
    flexShrink: 1,
  } as TextStyle,
  icon: {
    height: 16,
    width: 16,
    justifyContent: 'center',
    alignItems: 'center',
    flexShrink: 0,
  } as ViewStyle,
});
