import { getRealmInstance } from '~/db/realm/utils/realm.config';
import { RouteSummarySchema } from '~/db/realm/schemas/route-summary.schema';
import { StopSchema } from '~/db/realm/schemas/stop.schema';
import { RouteSummary } from '~/types/routes.types';
import { Stop } from '~/types/stops.types';
import { Parcel } from '~/types/parcel.types';
import { ParcelSchema } from '~/db/realm/schemas/parcel.schema';
import { IImage } from '~/types/image.types';
import { ImageSchema } from '~/db/realm/schemas/image.schema';
import { deleteCachedImages } from '~/utils/images';
import { safeWrite } from '~/db/realm/utils/safeRealm';
import { MAX_RETRY_COUNT } from '~/utils/constants';
import { RequestQueueSchema } from '~/db/realm/schemas/request-queue.schema';
import { IRequestQueue } from '~/types/request-queue.types';

const GARBAGE_COLLECTION_STATUS = {
  NOT_NEEDED: 'not_needed',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  ERROR: 'error',
};

type StatusType =
  (typeof GARBAGE_COLLECTION_STATUS)[keyof typeof GARBAGE_COLLECTION_STATUS];

async function purgeStops(
  onStatusUpdate?: (status: StatusType) => void,
): Promise<boolean> {
  try {
    const realm = await getRealmInstance();

    const allStops = realm.objects<Stop>(StopSchema.name);

    if (allStops.length === 0) {
      onStatusUpdate?.(GARBAGE_COLLECTION_STATUS.NOT_NEEDED);
      return true;
    }

    onStatusUpdate?.(GARBAGE_COLLECTION_STATUS.IN_PROGRESS);

    const allRoutes = realm.objects<RouteSummary>(RouteSummarySchema.name);
    const routeStatusMap = new Map(
      Array.from(allRoutes).map(route => [route.Id, route.Status__c]),
    );

    // Only use Realm objects inside transaction boundaries!
    const stopsToDelete = Array.from(allStops).filter(stop => {
      const routeStatus = routeStatusMap.get(stop.Summary__c);
      return !routeStatus;
    });

    if (stopsToDelete.length === 0) {
      onStatusUpdate?.(GARBAGE_COLLECTION_STATUS.NOT_NEEDED);
      return true;
    }

    safeWrite(realm, () => {
      realm.delete(stopsToDelete);
    });

    onStatusUpdate?.(GARBAGE_COLLECTION_STATUS.COMPLETED);
    return true;
  } catch (error) {
    console.error(
      'GarbageCollectionService.ts: purgeStops(): Failed to purge stops',
      error,
    );
    onStatusUpdate?.(GARBAGE_COLLECTION_STATUS.ERROR);
    return false;
  }
}

async function purgeParcels(
  onStatusUpdate?: (status: StatusType) => void,
): Promise<boolean> {
  try {
    const realm = await getRealmInstance();
    const stopIds = Array.from(realm.objects<Stop>(StopSchema.name)).map(
      stop => stop.Id,
    );
    const parcels = Array.from(realm.objects<Parcel>(ParcelSchema.name)).filter(
      parcel =>
        !stopIds.includes(parcel.Delivery__c) ||
        !stopIds.includes(parcel.Pickup__c),
    );

    if (parcels.length === 0) {
      onStatusUpdate?.(GARBAGE_COLLECTION_STATUS.NOT_NEEDED);
      return true;
    }

    safeWrite(realm, () => {
      realm.delete(parcels);
    });

    onStatusUpdate?.(GARBAGE_COLLECTION_STATUS.COMPLETED);
    return true;
  } catch (error) {
    console.error(
      'GarbageCollectionService.ts: purgeParcels(): Failed to purge parcels',
      error,
    );
    onStatusUpdate?.(GARBAGE_COLLECTION_STATUS.ERROR);
    return false;
  }
}

async function purgeImages(
  onStatusUpdate?: (status: StatusType) => void,
): Promise<boolean> {
  try {
    const realm = await getRealmInstance();
    const stopIds = Array.from(realm.objects<Stop>(StopSchema.name)).map(
      stop => stop.Id,
    );

    const images = Array.from(realm.objects<IImage>(ImageSchema.name)).filter(
      image => !stopIds.includes(image.StopId) && image.isSynced === true,
    );

    if (images.length === 0) {
      onStatusUpdate?.(GARBAGE_COLLECTION_STATUS.NOT_NEEDED);
      return true;
    }

    await deleteCachedImages();

    safeWrite(realm, () => {
      realm.delete(images);
    });

    onStatusUpdate?.(GARBAGE_COLLECTION_STATUS.COMPLETED);
    return true;
  } catch (error) {
    console.error(
      'GarbageCollectionService.ts: purgeImages(): Failed to purge images',
      error,
    );
    onStatusUpdate?.(GARBAGE_COLLECTION_STATUS.ERROR);
    return false;
  }
}

async function purgeFailedRequestQueue(
  onStatusUpdate?: (status: StatusType) => void,
): Promise<boolean> {
  try {
    const realm = await getRealmInstance();
    const failedRequests = realm
      .objects<IRequestQueue>(RequestQueueSchema.name)
      .filtered('retryCount >= $0', MAX_RETRY_COUNT);

    if (failedRequests.length === 0) {
      onStatusUpdate?.(GARBAGE_COLLECTION_STATUS.NOT_NEEDED);
      return true;
    }

    safeWrite(realm, () => {
      realm.delete(failedRequests);
    });

    onStatusUpdate?.(GARBAGE_COLLECTION_STATUS.COMPLETED);
    return true;
  } catch (error) {
    console.error(
      'GarbageCollectionService.ts: purgeFailedRequestQueue(): Failed to purge failed requests',
      error,
    );
    onStatusUpdate?.(GARBAGE_COLLECTION_STATUS.ERROR);
    return false;
  }
}

const runFullCleanup = async (
  onStatusUpdate?: (status: StatusType) => void,
): Promise<boolean> => {
  try {
    await purgeStops(onStatusUpdate);
    await purgeParcels(onStatusUpdate);
    await purgeImages(onStatusUpdate);
    await purgeFailedRequestQueue(onStatusUpdate);

    return true;
  } catch (error) {
    console.error(
      'garbageCollection.ts: runFullCleanup(): Failed to clean up',
      error,
    );
    onStatusUpdate?.(GARBAGE_COLLECTION_STATUS.ERROR);
    return false;
  }
};

export const GarbageCollector = {
  purgeStops,
  purgeParcels,
  purgeImages,
  runFullCleanup,
};
