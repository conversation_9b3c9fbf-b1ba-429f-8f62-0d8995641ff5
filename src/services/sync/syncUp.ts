import Realm from 'realm';
import { <PERSON><PERSON>, InteractionManager } from 'react-native';
import { postData } from '~/api/apiService';
import { API_ENDPOINT_KEYS } from '~/api/apiEndpoints';
import {
  IRequestQueue,
  RequestQueueStatus,
  RequestType,
} from '~/types/request-queue.types';
import { RequestQueueSchema } from '~/db/realm/schemas/request-queue.schema';
import { SyncResult, UpdateEntityProps } from '~/types/sync.types';
import { Parcel } from '~/types/parcel.types';
import { ParcelSchema } from '~/db/realm/schemas/parcel.schema';
import { ImageSchema } from '~/db/realm/schemas/image.schema';
import { IImage } from '~/types/image.types';
import { STOP_STATUS } from '~/utils/constants';
import { StopSchema } from '~/db/realm/schemas/stop.schema';
import { Stop } from '~/types/stops.types';
import { getRealmInstance } from '~/db/realm/utils/realm.config';
import { EntityName } from '~/db/realm/utils/constants';
import { RouteSummary } from '~/types/routes.types';
import { RouteSummarySchema } from '~/db/realm/schemas/route-summary.schema';
import { safeWrite } from '~/db/realm/utils/safeRealm';
import { SentryService } from '~/services/SentryService';

const BATCH_SIZE = 5;

export async function syncUp(
  realm: Realm,
  pendingRequests: IRequestQueue[],
): Promise<SyncResult[]> {
  try {
    const results = await processAllBatches(realm, pendingRequests);

    safeWrite(realm, () => {
      const successfulRequests = realm
        .objects(RequestQueueSchema.name)
        .filtered(
          'entityId IN $0',
          results.map(result => result.entityId),
        );
      realm.delete(successfulRequests);
    });

    return results;
  } catch (error) {
    console.error('syncUp.ts: syncUp(): Error syncing requests:', error);
    Alert.alert(`Sync up error: ${JSON.stringify(error)}`);
    return [];
  }
}

async function processAllBatches(
  realm: Realm,
  pendingRequests: IRequestQueue[],
): Promise<SyncResult[]> {
  const syncResults: SyncResult[] = [];

  const processBatch = async (startIndex: number): Promise<void> => {
    return new Promise(resolve => {
      InteractionManager.runAfterInteractions(async () => {
        const batch = pendingRequests.slice(
          startIndex,
          startIndex + BATCH_SIZE,
        );

        if (batch.length === 0) {
          resolve();
          return;
        }

        const results = await processBatchOfRequests(realm, batch);
        syncResults.push(...results);

        await processBatch(startIndex + BATCH_SIZE);
        resolve();
      });
    });
  };

  await processBatch(0);
  return syncResults;
}

async function processBatchOfRequests(
  realm: Realm,
  batch: IRequestQueue[],
): Promise<SyncResult[]> {
  try {
    const batchPromises = batch.map(request =>
      syncSingleRequest(realm, request),
    );
    const results = await Promise.allSettled(batchPromises);
    return results
      .map(result => {
        if (result.status === 'fulfilled') {
          return result.value;
        }
        return null;
      })
      .filter(result => result !== null);
  } catch (error) {
    console.error(
      'syncUp.ts: processBatchOfRequests():  Error processing batch:',
      error,
    );
    return [];
  }
}

const logSyncError = async ({
  error,
  request,
  response,
}: {
  error: any;
  request: IRequestQueue;
  response: any;
}) => {
  const { entityName, entityId, requestType, payload } = request;

  await SentryService.logSentryError({
    error,
    tags: {
      file: 'syncUp.ts',
      function: 'handleSyncResponse',
      entity: entityName,
      requestType,
    },
    extra: {
      payload,
      response,
    },
    contexts: {
      syncMeta: {
        entityId,
        entityName,
        requestType,
        timestamp: new Date().toISOString(),
      },
    },
    level: 'error',
  });

  console.error(
    `syncUp.ts: logSyncError():  Sync Error ${entityName}: ${entityId}`,
    JSON.stringify(error),
  );
};

const handleSyncResponse = ({
  realm,
  request,
  resolve,
  reject,
}: {
  realm: Realm;
  request: IRequestQueue;
  resolve: (value: SyncResult) => void;
  reject: (reason?: any) => void;
}) => {
  return async function (response: any, error: any) {
    if (error) {
      safeWrite(realm, () => {
        request.status = RequestQueueStatus.FAILED;
        request.retryCount = request.retryCount + 1;
        request.lastUpdatedAt = new Date();
      });

      await logSyncError({ error, request, response });

      reject(null);
    } else {
      if (request.entityName === EntityName.PARCEL) {
        updateParcelWithSalesforceId({ realm, request, response });
      } else if (request.entityName === EntityName.STOP) {
        updateStopImageStatus({ realm, request });
      } else if (request.entityName === EntityName.ROUTE_SUMMARY) {
        updateRouteImageStatus({ realm, request });
      }
      console.info(
        `Sync Response: ${request.entityName}: ${request.entityId}`,
        JSON.stringify(response),
      );
      resolve({
        entityId: request.entityId,
        entityName: request.entityName,
      });
    }
  };
};

async function syncSingleRequest(
  realm: Realm,
  request: IRequestQueue,
): Promise<SyncResult> {
  return new Promise((resolve, reject) => {
    const responseHandler = handleSyncResponse({
      realm,
      request,
      resolve,
      reject,
    });

    switch (request.entityName) {
      case EntityName.PARCEL:
        const updatedPayload = {
          ...request.payload,
          records: request.payload.records.map(
            ({ Id: _Id, ...rest }: Parcel) => ({
              ...rest,
            }),
          ),
        };
        postData({
          onSync: responseHandler,
          requestIdentifier: API_ENDPOINT_KEYS.POST_BATCH_SOBJECTS,
          requestData: updatedPayload,
        });
        break;

      case EntityName.STOP:
        postData({
          onSync: responseHandler,
          requestIdentifier: API_ENDPOINT_KEYS.PATCH_STOP,
          requestData: {
            ...(request.payload as object),
            entityId: request.entityId,
          },
          method: 'patch',
        });
        break;

      case EntityName.ROUTE_SUMMARY:
        postData({
          onSync: responseHandler,
          requestIdentifier: API_ENDPOINT_KEYS.PATCH_ROUTE_SUMMARY,
          requestData: {
            ...(request.payload as object),
            entityId: request.entityId,
          },
          method: 'patch',
        });
        break;
      default:
    }
  });
}

function updateParcelWithSalesforceId({
  realm,
  request,
  response,
}: {
  realm: Realm;
  request: IRequestQueue;
  response: { id: string; success: boolean }[];
}) {
  request.payload?.records?.forEach(
    ({ Id: localId }: Parcel, index: number) => {
      const { id: remoteId, success } = response?.[index] || {};

      if (success) {
        const parcelWithLocalId = realm.objectForPrimaryKey<Parcel>(
          ParcelSchema.name,
          localId,
        );

        // Update images linked to parcel
        const imagesLinkedToParcel = realm
          .objects<IImage>(ImageSchema.name)
          .filtered(`ParcelId == $0`, localId);

        safeWrite(realm, () => {
          imagesLinkedToParcel.forEach(image => {
            image.PathOnClient = image.PathOnClient.replace(localId, remoteId);
            image.ParcelId = remoteId;
            image.isSyncReady = true;
          });

          // Re-create parcel with remoteId as primary key cannot be modified
          const newParcel = { ...parcelWithLocalId, Id: remoteId };
          realm.create(ParcelSchema.name, newParcel);

          // Delete old parcel
          if (parcelWithLocalId) {
            realm.delete(parcelWithLocalId);
          }
        });
      }
    },
  );
}

function updateRouteImageStatus({
  realm,
  request,
}: {
  realm: Realm;
  request: IRequestQueue;
}) {
  const route = realm.objectForPrimaryKey<RouteSummary>(
    RouteSummarySchema.name,
    request.entityId,
  );

  const routeIsCompleted = request.payload.Survey_Complete__c === true;

  if (!routeIsCompleted || !route) return;

  // Mark images linked to route as ready for sync
  const imagesLinkedToRoute = realm
    .objects<IImage>(ImageSchema.name)
    .filtered(
      `RouteSummaryId == $0 && StopId == null && ParcelId == null`,
      route.Id,
    );

  safeWrite(realm, () => {
    imagesLinkedToRoute.forEach(image => {
      image.isSyncReady = true;
    });
  });
}

function updateStopImageStatus({
  realm,
  request,
}: {
  realm: Realm;
  request: IRequestQueue;
}) {
  const stop = realm.objectForPrimaryKey<Stop>(
    StopSchema.name,
    request.entityId,
  );

  const stopIsCompleted = request.payload.Status__c === STOP_STATUS.COMPLETE;

  if (!stopIsCompleted || !stop) return;

  // Mark images linked to stop as ready for sync
  const imagesLinkedToStop = realm
    .objects<IImage>(ImageSchema.name)
    .filtered(
      `StopId == $0 && RouteSummaryId == $1 && ParcelId == null`,
      stop.Id,
      stop.Summary__c,
    );

  safeWrite(realm, () => {
    imagesLinkedToStop.forEach(image => {
      image.isSyncReady = true;
    });
  });
}

export const addToRequestQueue = async ({
  entityId,
  entityName,
  updates,
  requestType = RequestType.UPDATE,
}: UpdateEntityProps): Promise<boolean> => {
  try {
    const realm = await getRealmInstance();

    // Check if entityName is not Parcel__c
    const existingRequest = realm.objectForPrimaryKey(
      RequestQueueSchema.name,
      entityId,
    );

    const payload = existingRequest
      ? { ...(existingRequest.payload as object), ...updates }
      : updates;

    safeWrite(realm, () => {
      realm.create(
        RequestQueueSchema.name,
        { entityId, entityName, requestType, payload },
        true,
      );
    });

    return true;
  } catch (error) {
    console.error(
      `syncUp.ts: addToRequestQueue(): Failed to add ${entityName}: ${entityId} updating entity:`,
      error,
    );
    return false;
  }
};
