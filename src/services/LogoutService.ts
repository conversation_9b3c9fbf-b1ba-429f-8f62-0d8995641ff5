import { RequestQueueSchema } from '~/db/realm/schemas/request-queue.schema';
import { ImageSchema } from '~/db/realm/schemas/image.schema';
import { IImage } from '~/types/image.types';
import { IRequestQueue, RequestQueueStatus } from '~/types/request-queue.types';
import { AuthService } from '~/services/AuthService';
import { cleanupLocationService } from '~/services/location/LocationService';
import { getRealmInstance } from '~/db/realm/utils/realm.config';
import { KeychainService } from '~/utils/keyChain';
import TokenService from '~/services/TokenService';

const LogoutErrorCodes = {
  FAILED_TO_RESET_KEYCHAIN: 'FAILED_TO_RESET_KEYCHAIN',
  FAILED_TO_LOGOUT: 'FAILED_TO_LOGOUT',
};

type LogoutActionResponse = {
  success: boolean;
  errorCode?: (typeof LogoutErrorCodes)[keyof typeof LogoutErrorCodes];
};

async function canLogout(): Promise<boolean> {
  const realm = await getRealmInstance();

  const pendingRequests = realm
    .objects<IRequestQueue>(RequestQueueSchema.name)
    .filter(request => request.status === RequestQueueStatus.PENDING);

  const pendingImages = realm
    .objects<IImage>(ImageSchema.name)
    .filter(image => image.isSynced === false);

  return pendingRequests.length === 0 && pendingImages.length === 0;
}

async function logout(): Promise<LogoutActionResponse> {
  try {
    await cleanupLocationService();
    const refreshToken = await TokenService.getRefreshToken();

    if (refreshToken) {
      await AuthService.revoke(refreshToken);

      global.userId = undefined;

      const isSuccessful = await KeychainService.removeTokenFromKeychain();

      if (!isSuccessful) {
        return {
          success: false,
          errorCode: LogoutErrorCodes.FAILED_TO_RESET_KEYCHAIN,
        };
      }

      clearOneSignalHandlers();
      logoutOneSignalUser();

      const realm = await getRealmInstance();

      realm.beginTransaction();
      realm.deleteAll();
      realm.commitTransaction();

      return { success: true };
    }
  } catch (error) {
    console.error('LogoutService: logout():', error);
  }

  return { success: false, errorCode: LogoutErrorCodes.FAILED_TO_LOGOUT };
}

async function logoutWithoutDeletingData(): Promise<LogoutActionResponse> {
  try {
    await cleanupLocationService();
    const refreshToken = await TokenService.getRefreshToken();

    if (refreshToken) {
      await AuthService.revoke(refreshToken);

      global.userId = undefined;

      const isSuccessful = await KeychainService.removeTokenFromKeychain();

      if (!isSuccessful) {
        return {
          success: false,
          errorCode: LogoutErrorCodes.FAILED_TO_RESET_KEYCHAIN,
        };
      }

      return { success: true };
    }
  } catch (error) {
    console.error('LogoutService: logoutWithoutDeletingData():', error);
  }

  return { success: false, errorCode: LogoutErrorCodes.FAILED_TO_LOGOUT };
}

const LogoutService = { logout, canLogout, logoutWithoutDeletingData };

export default LogoutService;
