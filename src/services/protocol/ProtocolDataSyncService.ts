import { addToRequestQueue } from '~/services/sync/syncUp';
import { ActionHandlerResult } from '~/services/protocol/ProtocolActionService';
import { updateEntity } from '~/db/realm/operations';
import { STOP_STATUS } from '~/utils/constants';
import { AddImagePayload, ImageStore } from '~/services/ImageStoreService';
import { RouteSummarySchema } from '~/db/realm/schemas/route-summary.schema';
import { getRealmInstance } from '~/db/realm/utils/realm.config';
import { markRouteCompleted } from '~/services/sync/driverActions';
import { RouteSummary } from '~/types/routes.types';
import { ProtocolVerificationRecord } from '~/types/protocol.types';
import uuid from 'react-native-uuid';
import { createProtocolVerification } from '~/db/realm/operations/protocol-verification.operations';

/**
 * NOTE:
 * This function is implemented based on the structure of the `save` action result, as shown below:
 *
 * {
 *   "status": "save",
 *   "objects": [
 *     {
 *       "name": "Stop__c",
 *       "fields": [
 *         "Is_Lockbox_Out__c",
 *         "SNO_Tags__c"
 *       ],
 *       "action": "update"
 *     }
 *   ],
 *   "transformedState": {
 *     "Stop__c.Is_Lockbox_Out__c": "NBO",
 *     "didKnockOnDoor": "KOD",
 *     "Stop__c.Proof_of_Service__c": "This is the sample data",
 *     "askWaitForSamples": "No",
 *     "Stop__c": {
 *       "SNO_Tags__c": "KOD;No"
 *     }
 *   }
 * }
 *
 * - The `objects` array defines which entity (`name`) and which fields to update.
 * - The `transformedState` contains both flat key-value pairs and nested structures.
 * - Field values are pulled from `transformedState` using either dot notation keys (e.g., `Stop__c.Field__c`)
 *   or by accessing the nested object under `transformedState[entityName]`.
 */

type SaveAction = Extract<ActionHandlerResult, { status: 'save' }>;

type EntityUpdate = {
  entityId: string;
  entityName: string;
  updates: Record<string, any>;
};

const SUPPORTED_ENTITY = {
  PROTOCOL_VERIFICATION: 'Protocol_Verification__c',
} as const;

type SupportedEntity = (typeof SUPPORTED_ENTITY)[keyof typeof SUPPORTED_ENTITY];

const isSupportedEntity = (
  entityName: string,
): entityName is SupportedEntity => {
  return Object.values(SUPPORTED_ENTITY).includes(
    entityName as SupportedEntity,
  );
};

const getEntityIdFromScenario = (
  entityName: string,
  scenario: Record<string, any>,
): string | undefined => {
  return scenario[`${entityName}.Id`];
};

const getFlattenedSortedImagesToUpload = (
  transformedState: any,
): AddImagePayload[] => {
  return Object.entries(transformedState?.imagesToUpload ?? {})
    .sort(([a], [b]) => b.localeCompare(a))
    .flatMap(([, value]) => (Array.isArray(value) ? value : [value]))
    .filter(Boolean);
};

const checkAndMarkRouteCompleted = async (
  routeId: string,
  entityName: string,
): Promise<void> => {
  try {
    const realm = await getRealmInstance();

    const route = realm.objectForPrimaryKey<RouteSummary>(
      RouteSummarySchema.name,
      routeId,
    );

    if (!route) {
      throw new Error(
        `[checkAndMarkRouteCompleted] Route not found: ${routeId}`,
      );
    }

    const routeStops = realm
      .objects(entityName)
      .filtered('Summary__c == $0', routeId);

    const allStopsCompleted = routeStops.every(
      stop => stop.Status__c === STOP_STATUS.COMPLETE,
    );

    if (allStopsCompleted) {
      markRouteCompleted(route);
    }
  } catch (error) {
    throw new Error(`[checkAndMarkRouteCompleted] Failed: ${error?.message}`);
  }
};

const buildEntityUpdates = (
  fields: string[],
  entityName: string,
  transformedState: any,
): Record<string, any> => {
  return fields.reduce<Record<string, any>>((acc, field) => {
    const nested = transformedState?.[entityName]?.[field];
    const flat = transformedState?.[`${entityName}.${field}`];
    const value = nested !== undefined ? nested : flat;

    if (value !== '' && value !== null && value !== undefined) {
      acc[field] = value;
    }
    return acc;
  }, {});
};

const processEntityUpdate = async (
  entityUpdate: EntityUpdate,
  scenario: Record<string, any>,
): Promise<boolean> => {
  const { entityId, entityName, updates } = entityUpdate;

  const updateSuccess = await updateEntity({ entityId, entityName, updates });
  if (!updateSuccess) {
    console.error(
      `[processEntityUpdate] Failed to update entity: ${entityName} (${entityId})`,
    );
    return false;
  }

  if (entityName === 'Stop__c' && updates.Status__c === STOP_STATUS.COMPLETE) {
    const routeId = scenario['Stop__c.Summary__c'];
    try {
      await checkAndMarkRouteCompleted(routeId, entityName);
    } catch (error) {
      console.error(
        '[processEntityUpdate] Failed to check route completion:',
        error,
      );
    }
  }

  const wasQueued = await addToRequestQueue({ entityId, entityName, updates });
  if (!wasQueued) {
    console.error(
      `[processEntityUpdate] Failed to queue entity: ${entityName} (${entityId})`,
    );
    return false;
  }

  return true;
};

const handleImageUploads = async (transformedState: any): Promise<boolean> => {
  const hasImagesToUpload =
    transformedState?.imagesToUpload &&
    Object.keys(transformedState.imagesToUpload).length > 0;

  if (!hasImagesToUpload) {
    return true;
  }

  try {
    const imagesToUpload = getFlattenedSortedImagesToUpload(transformedState);
    const imageUploadPromises = imagesToUpload.map(ImageStore.addImage);
    await Promise.all(imageUploadPromises);
    return true;
  } catch (error) {
    console.error('[handleImageUploads] Failed to save images:', error);
    return false;
  }
};

const createVerificationObject = async (
  entityName: string,
  updates: Record<string, any>,
): Promise<boolean> => {
  try {
    if (!isSupportedEntity(entityName)) {
      console.warn(
        `[createVerificationObject] Unsupported entity for creation: ${entityName}. ` +
          `Supported entities: ${Object.values(SUPPORTED_ENTITY).join(', ')}`,
      );
      return false;
    }

    if (!updates || typeof updates !== 'object') {
      console.warn(
        `[createVerificationObject] Invalid updates object: ${JSON.stringify(updates)}`,
      );
      return false;
    }

    const verificationData: ProtocolVerificationRecord = {
      Id: uuid.v4() as string,
      Protocol_Step_Id__c: updates?.Protocol_Step_Id__c ?? null,
      Protocol__c: updates?.Protocol__c ?? null,
      Field_Value__c: updates?.Field_Value__c ?? null,
      Daily_Schedule__c: updates?.Daily_Schedule__c ?? null,
    };
    await createProtocolVerification(verificationData);
    return true;
  } catch (error) {
    console.error(
      '[createVerificationObject] Failed to create verification object:',
      error,
    );
    return false;
  }
};

const processCreateAction = async (
  entityName: string,
  fields: string[],
  transformedState: any,
): Promise<boolean> => {
  const updates = buildEntityUpdates(fields, entityName, transformedState);
  const createSuccess = await createVerificationObject(entityName, updates);

  if (!createSuccess) {
    console.error(
      `[handleSaveProtocolAction] Failed to create verification object: ${entityName}`,
    );
  }

  return createSuccess;
};

const processUpdateAction = async (
  entityName: string,
  fields: string[],
  transformedState: any,
  scenario: Record<string, any>,
): Promise<boolean> => {
  const entityId = getEntityIdFromScenario(entityName, scenario);

  if (!entityId) {
    console.error(
      `[handleSaveProtocolAction] No ID found for entity: ${entityName}`,
    );
    return false;
  }

  const updates = buildEntityUpdates(fields, entityName, transformedState);

  if (Object.keys(updates).length === 0) {
    console.warn(
      `[handleSaveProtocolAction] No updates for entity: ${entityName}`,
    );
    return true;
  }

  // PATCH: Needs to be fixed in the future
  if (entityName === 'Stop__c' && updates.Completed_Time__c) {
    updates.Status__c = STOP_STATUS.COMPLETE;
  }

  const updateSuccess = await processEntityUpdate(
    { entityId, entityName, updates },
    scenario,
  );

  if (!updateSuccess) {
    console.error(
      `[handleSaveProtocolAction] Failed to update entity: ${entityName} (${entityId})`,
    );
  }

  return updateSuccess;
};

export const handleSaveProtocolAction = async (
  action: SaveAction,
  scenario: Record<string, any>,
): Promise<boolean> => {
  const { objects, transformedState } = action;
  let success = true;

  const imageUploadSuccess = await handleImageUploads(transformedState);
  if (!imageUploadSuccess) {
    success = false;
  }

  for (const { action: entityAction, name: entityName, fields } of objects) {
    let entitySuccess = false;

    if (entityAction === 'create') {
      entitySuccess = await processCreateAction(
        entityName,
        fields,
        transformedState,
      );
    } else {
      entitySuccess = await processUpdateAction(
        entityName,
        fields,
        transformedState,
        scenario,
      );
    }

    if (!entitySuccess) {
      success = false;
    }
  }

  return success;
};
