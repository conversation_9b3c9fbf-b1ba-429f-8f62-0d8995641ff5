import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import SignatureCapture from '~/components/SignatureCapture';

// Mock react-native-signature-canvas
jest.mock('react-native-signature-canvas', () => {
  const _React = require('react');
  const { forwardRef, useImperativeHandle, createElement } = _React;

  const SignatureScreen = forwardRef(
    ({ onOK, onClear, onGetData, onEnd, onBegin, ...props }: any, ref: any) => {
      useImperativeHandle(ref, () => ({
        clearSignature: () => onClear?.(),
        getSignature: () => onGetData?.('mocked-signature-data'),
        readSignature: () => onGetData?.('mocked-signature-data'),
      }));

      return createElement('View', {
        testID: 'signature-screen',
        onTouchStart: () => onBegin?.(),
        onTouchEnd: () => {
          onEnd?.();
          onOK?.('mocked-signature-data');
        },
        onPress: () => onOK?.('mocked-signature-data'),
        ...props,
      });
    },
  );

  return SignatureScreen;
});

// Mock the icon
jest.mock('~/assets/icons', () => ({
  BrushWithBackground: () => 'BrushWithBackground',
}));

describe('SignatureCapture', () => {
  const mockOnConfirmCallback = jest.fn();
  const mockIsUserSigningCallback = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    const { getByTestId } = render(
      <SignatureCapture onConfirmCallback={mockOnConfirmCallback} />,
    );

    expect(getByTestId('signature-screen')).toBeTruthy();
  });

  it('renders with signatureURI prop', () => {
    const { getByTestId } = render(
      <SignatureCapture
        onConfirmCallback={mockOnConfirmCallback}
        signatureURI="data:image/png;base64,mocked-data"
      />,
    );

    expect(getByTestId('signature-screen')).toBeTruthy();
  });

  it('calls isUserSigningCallback when signing begins', () => {
    const { getByTestId } = render(
      <SignatureCapture
        onConfirmCallback={mockOnConfirmCallback}
        isUserSigningCallback={mockIsUserSigningCallback}
      />,
    );

    const signatureScreen = getByTestId('signature-screen');
    fireEvent(signatureScreen, 'touchStart');

    expect(mockIsUserSigningCallback).toHaveBeenCalledWith(true);
  });

  it('calls isUserSigningCallback when signing ends', () => {
    const { getByTestId } = render(
      <SignatureCapture
        onConfirmCallback={mockOnConfirmCallback}
        isUserSigningCallback={mockIsUserSigningCallback}
      />,
    );

    const signatureScreen = getByTestId('signature-screen');
    fireEvent(signatureScreen, 'touchEnd');

    expect(mockIsUserSigningCallback).toHaveBeenCalledWith(false);
  });

  it('calls onConfirmCallback when signature is confirmed', async () => {
    const { getByTestId } = render(
      <SignatureCapture onConfirmCallback={mockOnConfirmCallback} />,
    );

    const signatureScreen = getByTestId('signature-screen');
    fireEvent(signatureScreen, 'touchEnd');

    await waitFor(() => {
      expect(mockOnConfirmCallback).toHaveBeenCalledWith(
        'mocked-signature-data',
      );
    });
  });

  it('handles clear signature action', async () => {
    const { getByTestId } = render(
      <SignatureCapture onConfirmCallback={mockOnConfirmCallback} />,
    );

    const brushIcon = getByTestId('brush-icon');
    fireEvent.press(brushIcon);

    await waitFor(() => {
      expect(mockOnConfirmCallback).toHaveBeenCalledWith('');
    });
  });

  it('renders brush icon for clearing signature', () => {
    const { getByTestId } = render(
      <SignatureCapture onConfirmCallback={mockOnConfirmCallback} />,
    );

    expect(getByTestId('brush-icon')).toBeTruthy();
  });

  it('does not call isUserSigningCallback when not provided', () => {
    const { getByTestId } = render(
      <SignatureCapture onConfirmCallback={mockOnConfirmCallback} />,
    );

    const signatureScreen = getByTestId('signature-screen');
    fireEvent(signatureScreen, 'touchStart');
    fireEvent(signatureScreen, 'touchEnd');

    expect(mockIsUserSigningCallback).not.toHaveBeenCalled();
  });

  it('handles multiple signature sessions', async () => {
    const { getByTestId } = render(
      <SignatureCapture
        onConfirmCallback={mockOnConfirmCallback}
        isUserSigningCallback={mockIsUserSigningCallback}
      />,
    );

    const signatureScreen = getByTestId('signature-screen');

    // First signature session
    fireEvent(signatureScreen, 'touchStart');
    fireEvent(signatureScreen, 'touchEnd');

    // Second signature session
    fireEvent(signatureScreen, 'touchStart');
    fireEvent(signatureScreen, 'touchEnd');

    await waitFor(() => {
      expect(mockIsUserSigningCallback).toHaveBeenCalledTimes(4);
      expect(mockOnConfirmCallback).toHaveBeenCalledTimes(2);
    });
  });
});
