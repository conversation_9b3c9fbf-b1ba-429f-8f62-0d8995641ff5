import { Location } from 'react-native-background-geolocation';
import { API_ENDPOINT_KEYS } from '~/api/apiEndpoints';
import { postData } from '~/api/apiService';
import { mapActivityType } from '~/utils/location';
import { isDeviceOnline } from '~/utils/network';
import { getDataFromRealm, saveLocation } from '~/db/realm';
import { Location as LocationType } from '~/types/location.types';

export const postLocationToServer = async (
  location: Location,
  routeId?: string | null,
  stopId?: string | null,
  isCheckIn?: boolean,
) => {
  const isCachedFormat = location && 'Location__Latitude__s' in location;

  if (!routeId) {
    routeId = await getDataFromRealm<string | null>('routeId');
  }

  if (!stopId) {
    stopId = await getDataFromRealm<string | null>('stopId');
  }

  if (!stopId) {
    console.info(
      'Could not send location to backend as no eligible stop is there',
    );
    return;
  }

  const reqData: LocationType = isCachedFormat
    ? {
        Location__Latitude__s: location.Location__Latitude__s,
        Location__Longitude__s: location.Location__Longitude__s,
        Route_Summary__c: location.Route_Summary__c ?? routeId ?? '',
        Stop__c: location.Stop__c ?? stopId,
        Timestamp__c: location.Timestamp__c,
        Activity_Type__c: isCheckIn
          ? 'Check In'
          : mapActivityType(location.Activity_Type__c),
        Speed__c:
          location.Speed__c !== undefined && location.Speed__c >= 0
            ? location.Speed__c
            : 0,
        Heading__c:
          location.Heading__c !== undefined && location.Heading__c >= 0
            ? location.Heading__c
            : undefined,
      }
    : {
        Location__Latitude__s: location.coords?.latitude,
        Location__Longitude__s: location.coords?.longitude,
        Route_Summary__c: routeId ?? '',
        Stop__c: stopId,
        Timestamp__c: location.timestamp,
        Activity_Type__c: isCheckIn
          ? 'Check In'
          : mapActivityType(location.activity?.type),
        Speed__c:
          location.coords?.speed !== undefined && location.coords.speed >= 0
            ? location.coords.speed
            : 0,
        Heading__c:
          location.coords?.heading !== undefined && location.coords.heading >= 0
            ? location.coords.heading
            : undefined,
      };

  console.info('Posting location to backend:', reqData);

  const handleAPIResponse = (apiResponse: any, apiError?: string) => {
    if (apiError) {
      console.error(`location.ts: handleAPIResponse(): ${apiError}`);
    } else if (apiResponse) {
      console.info(
        `location.ts: handleAPIResponse(): ${JSON.stringify(apiResponse)}`,
      );
    }
  };

  const post = async (data: any) => {
    try {
      await postData({
        onSync: handleAPIResponse,
        requestIdentifier: API_ENDPOINT_KEYS.POST_HEARTBEAT_LOCATION,
        requestData: data,
      });
    } catch (err: any) {
      console.error(`Error: ${err.message}`);
    }
  };

  try {
    const online = await isDeviceOnline();

    if (!online) {
      console.info('Internet issue--Unable to post location, saving to db');
      await saveLocation(reqData);
      return;
    }

    await post(reqData);
    console.info('[postLocationToServer] Location posted successfully.');
  } catch (error) {
    console.error('[postLocationToServer] Failed to post location:', error);
    await saveLocation(reqData);
  }
};
