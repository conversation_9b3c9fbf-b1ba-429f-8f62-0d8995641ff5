import Realm from 'realm';
import { SCHEMAS } from '~/db/realm/schemas';
import { migrateRealm } from '~/db/realm/utils/migration';

let realmInstance: Realm | null = null;

export async function getRealmInstance(): Promise<Realm> {
  if (!realmInstance || realmInstance.isClosed) {
    realmInstance = await Realm.open({
      schema: SCHEMAS,
      schemaVersion: 11, // bump version when adding new migration
      onMigration: migrateRealm,
    });

    if (__DEV__) {
      // Get on-disk location of the Realm
      const realmFileLocation = realmInstance.path;
      console.info(
        `realm.config.ts: getRealmInstance(): Realm file is located at: ${realmFileLocation}`,
      );
    }
  }

  return realmInstance;
}
