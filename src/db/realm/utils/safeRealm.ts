import { getRealmInstance } from '~/db/realm/utils/realm.config';
import { Realm } from '@realm/react';

/**
 * Deeply clones a Realm object or array into a plain JS object/array.
 * Handles nested structures and plain values.
 */
export function deepCopy<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') return obj;
  if (Array.isArray(obj)) return obj.map(deepCopy) as any;
  if (typeof (obj as any).toJSON === 'function') {
    return (obj as any).toJSON();
  }
  const result: any = {};
  for (const key in obj) {
    result[key] = deepCopy((obj as any)[key]);
  }
  return result;
}

/**
 * Safely get a Realm object by primary key and return a plain JS copy.
 */
export async function safeGetByPrimaryKey<T>(
  schema: string,
  key: any,
): Promise<T | null> {
  const realm = await getRealmInstance();
  if (!realm || realm.isClosed) return null;
  const obj = realm.objectForPrimaryKey<T>(schema, key);
  return obj ? deepCopy(obj) : null;
}

/**
 * Safely query Realm for all objects in a schema and return an array of plain JS copies.
 */
export async function safeQueryAll<T>(schema: string): Promise<T[]> {
  const realm = await getRealmInstance();
  if (!realm || realm.isClosed) return [];
  const results = realm.objects<T>(schema);
  return results.map(deepCopy);
}

/**
 * Safely query Realm with a filter and return an array of plain JS copies.
 */
export async function safeQueryFiltered<T>(
  schema: string,
  filter: string,
  args?: unknown[],
): Promise<T[]> {
  const realm = await getRealmInstance();
  if (!realm || realm.isClosed) return [];
  const results = args
    ? realm.objects<T>(schema).filtered(filter, ...args)
    : realm.objects<T>(schema).filtered(filter);
  return results.map(deepCopy);
}

/**
 * Safe write: runs fn in a transaction, or inside existing transaction if already open.
 */
export function safeWrite(realm: Realm, fn: () => void) {
  try {
    if (realm.isInTransaction) {
      fn();
    } else {
      realm.write(fn);
    }
  } catch (error) {
    console.error('Error during Realm write operation:', error);
    throw error;
  }
}
