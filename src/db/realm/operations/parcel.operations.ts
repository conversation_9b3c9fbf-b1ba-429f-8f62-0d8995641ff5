import { Parcel, ParcelType } from '~/types/parcel.types';
import { getRealmInstance } from '~/db/realm/utils/realm.config';
import {
  ParcelSchema,
  ParcelTypeSchema,
} from '~/db/realm/schemas/parcel.schema';
import {
  safeQueryAll,
  safeQueryFiltered,
  safeWrite,
} from '~/db/realm/utils/safeRealm';

const saveParcels = async (parcels: Parcel[]) => {
  try {
    const realm = await getRealmInstance();

    safeWrite(realm, () => {
      parcels.forEach(parcel => {
        if (
          (parcel.Quantity__c || parcel.Quantity_Expected__c) &&
          parcel.Parcel_Type_Definition__c
        ) {
          realm.create(ParcelSchema.name, parcel, true);
        }
      });
    });
  } catch (error) {
    console.error('parcel.operations.ts: saveParcels():', error);
    throw error;
  }
};

const saveParcelTypes = async (parcelTypes: ParcelType[]) => {
  try {
    const realm = await getRealmInstance();

    safeWrite(realm, () => {
      parcelTypes.forEach(({ Id, Name }) => {
        realm.create(ParcelTypeSchema.name, { Id, Name }, true);
      });
    });
  } catch (error) {
    console.error('parcel.operations.ts: saveParcelTypes():', error);
    throw error;
  }
};

const getParcelTypes = async (): Promise<ParcelType[]> => {
  try {
    return await safeQueryAll<ParcelType>(ParcelTypeSchema.name);
  } catch (error) {
    console.error('parcel.operations.ts: getParcelTypes():', error);
    throw error;
  }
};

const getPickupParcelsByStopId = async (stopId: string): Promise<Parcel[]> => {
  try {
    return await safeQueryFiltered<Parcel>(
      ParcelSchema.name,
      'Pickup__c = $0',
      [stopId],
    );
  } catch (error) {
    console.error('parcel.operations.ts: getPickupParcelsByStopId():', error);
    throw error;
  }
};

const getDeliveryParcelsByStopId = async (
  stopId: string,
): Promise<Parcel[]> => {
  try {
    return await safeQueryFiltered<Parcel>(
      ParcelSchema.name,
      'Delivery__c = $0',
      [stopId],
    );
  } catch (error) {
    console.error('parcel.operations.ts: getDeliveryParcelsByStopId():', error);
    throw error;
  }
};

export {
  saveParcels,
  saveParcelTypes,
  getParcelTypes,
  getPickupParcelsByStopId,
  getDeliveryParcelsByStopId,
};
