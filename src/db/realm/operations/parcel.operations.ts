import { Parcel, ParcelType } from '~/types/parcel.types';
import { getRealmInstance } from '~/db/realm/utils/realm.config';
import {
  ParcelSchema,
  ParcelTypeSchema,
} from '~/db/realm/schemas/parcel.schema';
import {
  safeQueryAll,
  safeQueryFiltered,
  safeWrite,
} from '~/db/realm/utils/safeRealm';

const saveParcels = async (parcels: Parcel[]) => {
  try {
    const realm = await getRealmInstance();

    safeWrite(realm, () => {
      parcels.forEach(parcel => {
        if (parcel.Quantity__c && parcel.Parcel_Type_Definition__c) {
          realm.create(ParcelSchema.name, parcel, true);
        }
      });
    });
  } catch (error) {
    console.error('parcel.operations.ts: saveParcels():', error);
    throw error;
  }
};

const saveParcelTypes = async (parcelTypes: ParcelType[]) => {
  try {
    const realm = await getRealmInstance();

    if (parcelTypes.length === 0) {
      const rawSample = [
        {
          attributes: {
            type: 'Parcel_Type__c',
            url: '/services/data/v61.0/sobjects/Parcel_Type__c/a3G3h000000XH2kEAG',
          },
          Id: 'a3G3h000000XH2kEAG',
          Name: 'Box',
        },
        {
          attributes: {
            type: 'Parcel_Type__c',
            url: '/services/data/v61.0/sobjects/Parcel_Type__c/a3G3h000000XH2pEAG',
          },
          Id: 'a3G3h000000XH2pEAG',
          Name: 'Mail',
        },
        {
          attributes: {
            type: 'Parcel_Type__c',
            url: '/services/data/v61.0/sobjects/Parcel_Type__c/a3G3h000000XH2qEAG',
          },
          Id: 'a3G3h000000XH2qEAG',
          Name: 'Envelope',
        },
        {
          attributes: {
            type: 'Parcel_Type__c',
            url: '/services/data/v61.0/sobjects/Parcel_Type__c/a3GTR000005BpcD2AS',
          },
          Id: 'a3GTR000005BpcD2AS',
          Name: 'Test Parcel',
        },
        {
          attributes: {
            type: 'Parcel_Type__c',
            url: '/services/data/v61.0/sobjects/Parcel_Type__c/a3GTR000005Bpdp2AC',
          },
          Id: 'a3GTR000005Bpdp2AC',
          Name: 'Test Parcel 2',
        },
        {
          attributes: {
            type: 'Parcel_Type__c',
            url: '/services/data/v61.0/sobjects/Parcel_Type__c/a3GTR000005kBbt2AE',
          },
          Id: 'a3GTR000005kBbt2AE',
          Name: 'Box',
        },
        {
          attributes: {
            type: 'Parcel_Type__c',
            url: '/services/data/v61.0/sobjects/Parcel_Type__c/a3GTR000005kBaH2AU',
          },
          Id: 'a3GTR000005kBaH2AU',
          Name: 'Specimen',
        },
        {
          attributes: {
            type: 'Parcel_Type__c',
            url: '/services/data/v61.0/sobjects/Parcel_Type__c/a3GTR000005kBYf2AM',
          },
          Id: 'a3GTR000005kBYf2AM',
          Name: 'Biopsy',
        },
        {
          attributes: {
            type: 'Parcel_Type__c',
            url: '/services/data/v61.0/sobjects/Parcel_Type__c/a3GTR000005kBVR2A2',
          },
          Id: 'a3GTR000005kBVR2A2',
          Name: 'Medication',
        },
        {
          attributes: {
            type: 'Parcel_Type__c',
            url: '/services/data/v61.0/sobjects/Parcel_Type__c/a3GTR000005kBTp2AM',
          },
          Id: 'a3GTR000005kBTp2AM',
          Name: 'Envelope',
        },
      ];
      parcelTypes = rawSample.map(pt => ({ Id: pt.Id, Name: pt.Name }));
    }

    safeWrite(realm, () => {
      parcelTypes.forEach(({ Id, Name }) => {
        realm.create(ParcelTypeSchema.name, { Id, Name }, true);
      });
    });
  } catch (error) {
    console.error('parcel.operations.ts: saveParcelTypes():', error);
    throw error;
  }
};

const getParcelTypes = async (): Promise<ParcelType[]> => {
  try {
    return await safeQueryAll<ParcelType>(ParcelTypeSchema.name);
  } catch (error) {
    console.error('parcel.operations.ts: getParcelTypes():', error);
    throw error;
  }
};

const getPickupParcelsByStopId = async (stopId: string): Promise<Parcel[]> => {
  try {
    return await safeQueryFiltered<Parcel>(
      ParcelSchema.name,
      'Pickup__c = $0',
      [stopId],
    );
  } catch (error) {
    console.error('parcel.operations.ts: getPickupParcelsByStopId():', error);
    throw error;
  }
};

const getDeliveryParcelsByStopId = async (
  stopId: string,
): Promise<Parcel[]> => {
  try {
    return await safeQueryFiltered<Parcel>(
      ParcelSchema.name,
      'Delivery__c = $0',
      [stopId],
    );
  } catch (error) {
    console.error('parcel.operations.ts: getDeliveryParcelsByStopId():', error);
    throw error;
  }
};

export {
  saveParcels,
  saveParcelTypes,
  getParcelTypes,
  getPickupParcelsByStopId,
  getDeliveryParcelsByStopId,
};
