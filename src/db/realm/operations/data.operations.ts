import { getRealmInstance } from '~/db/realm/utils/realm.config';
import { DataSchema } from '~/db/realm/schemas/data.schema';
import { safeGetByPrimaryKey, safeWrite } from '~/db/realm/utils/safeRealm';

const saveDataToRealm = async <T>(id: string, data: T) => {
  try {
    const realm = await getRealmInstance();
    safeWrite(realm, () => {
      realm.create(DataSchema.name, { _id: id, data }, true);
    });
  } catch (error) {
    console.error('Error saving data to db:', error);
    throw error;
  }
};

const getDataFromRealm = async <T>(id: string): Promise<T | null> => {
  try {
    const record = await safeGetByPrimaryKey<{ _id: string; data: T }>(
      DataSchema.name,
      id,
    );
    return record ? record.data : null;
  } catch (error) {
    console.error('Error getting data from db:', error);
    throw error;
  }
};

export { saveDataToRealm, getDataFromRealm };
