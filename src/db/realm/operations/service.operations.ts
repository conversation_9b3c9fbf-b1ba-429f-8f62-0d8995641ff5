import { Service, ServiceType } from '~/types/service.types';
import { getRealmInstance } from '~/db/realm/utils/realm.config';
import {
  ServiceSchema,
  ServiceTypeSchema,
} from '~/db/realm/schemas/service.schema';
import { safeWrite } from '~/db/realm/utils/safeRealm';

const saveServices = async (services: Service[]) => {
  try {
    const realm = await getRealmInstance();

    safeWrite(realm, () => {
      services.forEach(service => {
        realm.create(
          ServiceSchema.name,
          {
            ...service,
            attributes:
              typeof service.attributes === 'string'
                ? service.attributes
                : JSON.stringify(service.attributes ?? {}),
          },
          true,
        );
      });
    });
  } catch (error) {
    console.error('service.operations.ts: saveServices():', error);
    throw error;
  }
};

const saveServiceTypes = async (serviceTypes: ServiceType[]) => {
  try {
    const realm = await getRealmInstance();

    safeWrite(realm, () => {
      serviceTypes.forEach(serviceType => {
        realm.create(
          ServiceTypeSchema.name,
          {
            Id: serviceType.Id,
            Name: serviceType.Name,
          },
          true,
        );
      });
    });
  } catch (error) {
    console.error('parcel.operations.ts: saveServiceTypes():', error);
    throw error;
  }
};

export { saveServices, saveServiceTypes };
