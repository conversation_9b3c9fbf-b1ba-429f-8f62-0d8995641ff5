import { Stop, StopWithRelations } from '~/types/stops.types';
import { getRealmInstance } from '~/db/realm/utils/realm.config';
import { StopSchema } from '~/db/realm/schemas/stop.schema';
import { saveServices } from '~/db/realm/operations/service.operations';
import { saveParcels } from '~/db/realm/operations/parcel.operations';
import { safeQueryFiltered, safeWrite } from '~/db/realm/utils/safeRealm';

const saveStops = async (stops: StopWithRelations[]) => {
  if (stops && stops.length > 0) {
    try {
      const realm = await getRealmInstance();

      safeWrite(realm, () => {
        stops.forEach(stop => {
          realm.create(
            StopSchema.name,
            {
              ...stop,
              attributes:
                typeof stop.attributes === 'string'
                  ? stop.attributes
                  : JSON.stringify(stop.attributes ?? {}),
            },
            true,
          );
        });
      });

      for (const stop of stops) {
        const stopServices = stop.Services__r?.records ?? [];
        const stopParcels = [
          ...(stop.Pickup_Parcels__r?.records ?? []),
          ...(stop.Delivery_Parcels__r?.records ?? []),
        ];

        if (stopParcels.length > 0) {
          try {
            await saveParcels(stopParcels);
          } catch (err) {
            console.error(
              'stop.operations.ts: saveParcels(): stop: ',
              stop.Id,
              err,
            );
          }
        }

        if (stopServices.length > 0) {
          try {
            await saveServices(stopServices);
          } catch (err) {
            console.error(
              'stop.operations.ts: saveServices(): stop: ',
              stop.Id,
              err,
            );
          }
        }
      }
    } catch (error) {
      console.error('stop.operations.ts: saveStops():', error);
      throw error;
    }
  }
};

const getStopsByRouteId = async (routeId: string): Promise<Stop[]> => {
  try {
    return await safeQueryFiltered<Stop>(
      StopSchema.name,
      'Summary__c = $0 AND Completed_Time__c == null',
      [routeId],
    );
  } catch (error) {
    console.error('stop.operations.ts: getStopsByRouteId():', error);
    throw error;
  }
};

const getStopsByDate = async (date: string): Promise<StopWithRelations[]> => {
  try {
    return await safeQueryFiltered<StopWithRelations>(
      StopSchema.name,
      'Post_Date__c = $0 AND Completed_Time__c == null AND Status__c != "COMPLETED" AND Status__c != "CANCELLED" AND Status__c != "INACTIVE"',
      [date],
    );
  } catch (error) {
    console.error('stop.operations.ts: getStopsByDate():', error);
    throw error;
  }
};

const updateStopById = async (stopId: string, updates: Partial<Stop>) => {
  try {
    const realm = await getRealmInstance();

    safeWrite(realm, () => {
      realm.create(
        StopSchema.name,
        {
          Id: stopId,
          ...updates,
        },
        true,
      );
    });
  } catch (error) {
    console.error('stop.operations.ts: updateStopById(): ', error);
    throw error;
  }
};

const getStopNameById = async (stopId: string): Promise<string> => {
  if (!stopId || stopId === '') {
    return '';
  }

  try {
    const stop = await safeQueryFiltered<StopWithRelations>(
      StopSchema.name,
      'Id = $0',
      [stopId],
    );

    return stop[0]?.Name ?? '';
  } catch (error) {
    console.error('stop.operations.ts: getStopNameById():', error);
    return '';
  }
};

export { saveStops, getStopsByRouteId, updateStopById, getStopsByDate, getStopNameById };
