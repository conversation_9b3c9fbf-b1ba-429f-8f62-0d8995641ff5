import React, { useEffect, useState } from 'react';
import {
  ScrollView,
  Text,
  StyleSheet,
  View,
  TextStyle,
  ViewStyle,
  Pressable,
  ActivityIndicator,
} from 'react-native';
import Config from 'react-native-config';
import DeviceInfo from 'react-native-device-info';
import en from '~/localization/en';
import { getPhoneAvailableDiskSpace } from '~/utils/deviceInfo';
import { row, contentWidth, shadowSmall } from '~/styles/views';
import { buttonText, blackishText, h3, grayishText } from '~/styles/text';
import { gap24 } from '~/styles/spacing';
import colors from '~/styles/colors';
import ScreenWrapper from '~/screens/ScreenWrapper';
import InfoRow from '~/components/profile/InfoRow';
import CustomModal from '~/components/modals/CustomModal';
import SalesforceImage from '~/components/images/SalesforceImage';
import {
  LogoutWithBackground,
  WifiWarningWithBackground,
} from '~/assets/icons';
import { DataSchema } from '~/db/realm/schemas/data.schema';
import { IUserProfile } from '~/types/users.types';
import { useQuery } from '@realm/react';
import { IData } from '~/types/data.types';

import TokenService from '~/services/TokenService';
import LogoutService from '~/services/LogoutService';
import { interpolateString } from '~/utils/strings';
import { useBottomTabBarHeight } from '@react-navigation/bottom-tabs';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Arrow, Exit } from '~/components/icons';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '~/navigation/RouteStack';

interface DeviceInfoState {
  manufacturer: string;
  model: string;
  OSVersion: string;
  appVersion: string;
  freeStorage: string;
}

type LogoutModalStateType = 'closed' | 'allowed' | 'notAllowed';

type ModalInfoType = {
  title: string;
  description: string;
  icon: React.ReactNode;
  primaryActionTitle: string;
  secondaryActionTitle: string;
};

type ModalInfo = {
  closed: Record<string, never>;
  allowed: ModalInfoType;
  notAllowed: ModalInfoType;
};

const MODAL_INFO: ModalInfo = {
  closed: {},
  allowed: {
    title: en.logout,
    description: en.logout_info,
    primaryActionTitle: en.logout,
    secondaryActionTitle: en.cancel,
    icon: <LogoutWithBackground />,
  },
  notAllowed: {
    title: en.logout_with_unsaved_data,
    description: en.logout_warning,
    icon: <WifiWarningWithBackground />,
    primaryActionTitle: en.cancel,
    secondaryActionTitle: en.logout,
  },
};

const ProfileScreen = ({
  navigation,
}: {
  navigation: NativeStackNavigationProp<RootStackParamList>;
}) => {
  const { bottom } = useSafeAreaInsets();
  const bottomTabBarHeight = useBottomTabBarHeight();

  const [logoutModalState, setLogoutModalState] =
    useState<LogoutModalStateType>('closed');

  const [deviceInfo, setDeviceInfo] = useState<DeviceInfoState>({
    manufacturer: en.unknown,
    model: en.unknown,
    OSVersion: en.unknown,
    appVersion: en.unknown,
    freeStorage: en.unknown,
  });
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const userId = global.userId;
  const isModalVisible = logoutModalState !== 'closed';

  const userDataRecord = useQuery<IData<IUserProfile>>({
    type: DataSchema.name,
    query: records => records.filtered('_id == $0', userId),
  });
  const userData = userDataRecord[0]?.data ?? null;

  const profileImageUrl = `${Config.PROFILE_PHOTO_URL_PREFIX}${userData.MediumPhotoUrl}`;

  // const language = userData.LanguageLocaleKey;

  const logout = async () => {
    setIsLoading(true);
    const isSuccess = await LogoutService.logout();
    setIsLoading(false);

    if (isSuccess) {
      setLogoutModalState('closed');
      navigation.navigate('AuthStack', { screen: 'Login' });
    }
  };

  const openLogoutModal = async () => {
    const isAllowedToLogout = await LogoutService.canLogout();

    if (isAllowedToLogout) {
      setLogoutModalState('allowed');
    } else {
      setLogoutModalState('notAllowed');
    }
  };

  useEffect(() => {
    const getDeviceInfo = async () => {
      try {
        const availableStorageInBytes = await getPhoneAvailableDiskSpace();

        setDeviceInfo({
          manufacturer: DeviceInfo.getBrand(),
          model: DeviceInfo.getModel(),
          OSVersion: DeviceInfo.getSystemVersion(),
          appVersion: DeviceInfo.getReadableVersion(),
          freeStorage: availableStorageInBytes ?? en.unknown,
        });
      } catch (err) {
        console.error(`getDeviceInfo(): Error fetching device info: ${err}`);
      }
    };

    const setAccessToken = async () => {
      try {
        const token = await TokenService.getAccessToken();
        global.accessToken = token;
      } catch (error) {
        console.error('Failed to fetch access token:', error);
      }
    };

    getDeviceInfo();
    setAccessToken();
  }, []);

  if (!userData) {
    return (
      <View style={styles.loaderContainer}>
        <ActivityIndicator color={colors.grey600} />
        <Text style={grayishText}>Loading...</Text>
      </View>
    );
  }

  const scrollContainerStyle = {
    ...styles.scrollContainer,
    paddingBottom:
      bottomTabBarHeight +
      (bottom || styles.scrollContainer.padding) +
      styles.scrollContainer.padding,
  };

  const closeModal = () => {
    setLogoutModalState('closed');
  };

  const renderLogoutModal = () => {
    if (!isModalVisible) return null;

    const {
      title,
      description,
      icon,
      primaryActionTitle,
      secondaryActionTitle,
    } = MODAL_INFO[logoutModalState];

    return (
      <CustomModal
        isVisible={isModalVisible}
        headerIcon={icon}
        headerText={title}
        descriptionText={interpolateString(description, {
          name: userData.Name,
        })}
        okButtonText={primaryActionTitle}
        cancelButtonText={secondaryActionTitle}
        onOkPress={logoutModalState === 'allowed' ? logout : closeModal}
        onCancelPress={logoutModalState === 'allowed' ? closeModal : logout}
        onClose={closeModal}
        disabled={isLoading}
      />
    );
  };

  return (
    <ScreenWrapper color={colors.backgroundLight}>
      <ScreenWrapper.Body withoutPadding>
        <ScrollView
          contentContainerStyle={scrollContainerStyle}
          showsVerticalScrollIndicator={false}>
          <View style={styles.div}>
            {renderLogoutModal()}

            <View style={styles.profileHeader}>
              <SalesforceImage
                imageUrl={profileImageUrl}
                accessToken={global.accessToken}
                style={styles.profileImage}
              />
              <Text style={styles.profileHeaderText}>{userData.Name}</Text>
            </View>

            <View style={[gap24, styles.cardLayout]}>
              <Text style={styles.text}>{en.user_information}</Text>

              <View style={styles.nameParent}>
                <InfoRow label={en.email} text={userData.Email} />
                <View style={styles.divider} />

                <InfoRow label={en.org_id} text={userData.OwnerId} />
                <View style={styles.divider} />

                <InfoRow label={en.user_id} text={userData.Id} />
              </View>
            </View>

            <View style={[gap24, styles.cardLayout]}>
              <Text style={styles.text}>{en.device_information}</Text>

              <View style={styles.nameParent}>
                <InfoRow
                  label={en.manufacturer}
                  text={deviceInfo.manufacturer}
                />
                <View style={styles.divider} />

                <InfoRow label={en.model} text={deviceInfo.model} />
                <View style={styles.divider} />

                <InfoRow label={en.os_version} text={deviceInfo.OSVersion} />
                <View style={styles.divider} />

                <InfoRow label={en.app_version} text={deviceInfo.appVersion} />
                <View style={styles.divider} />

                <InfoRow
                  label={en.free_storage}
                  text={deviceInfo.freeStorage}
                />
              </View>
            </View>
            <Pressable onPress={() => navigation.navigate('LanguageSelection')}>
              <View style={[styles.cardLayout, row] as ViewStyle}>
                <Text style={[buttonText, blackishText] as TextStyle}>
                  {en.language}
                </Text>
                {/* TODO: pull text from localization of selected language */}
                <Text style={styles.secondaryButtonText as TextStyle}>
                  English
                </Text>
                <Arrow color={colors.red600} />
              </View>
            </Pressable>
            <Pressable onPress={() => openLogoutModal()}>
              <View style={[styles.cardLayout, row] as ViewStyle}>
                <Text style={[buttonText, blackishText] as TextStyle}>
                  {en.logout}
                </Text>
                <Exit />
              </View>
            </Pressable>
          </View>
        </ScrollView>
      </ScreenWrapper.Body>
    </ScreenWrapper>
  );
};

const styles = StyleSheet.create({
  loaderContainer: {
    height: '87%',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8,
  },
  scrollContainer: {
    padding: 16,
  },
  card: {
    paddingVertical: 16,
    paddingHorizontal: 18,
    borderRadius: 16,
    backgroundColor: colors.white,
    width: '100%',
    flex: 1,
  },
  cardLayout: {
    padding: 16,
    width: contentWidth,
    borderRadius: 16,
    backgroundColor: colors.white,
    ...shadowSmall,
    justifyContent: 'space-between',
  } as ViewStyle,
  secondaryButtonText: {
    ...grayishText,
    fontSize: 18,
    textAlign: 'right',
    color: colors.grey900,
    flex: 1,
    marginRight: 16,
    marginLeft: 8,
  } as TextStyle,
  text: {
    ...h3,
    fontWeight: '500',
    fontSize: 18,
    marginRight: 16,
    marginBottom: 6,
    color: colors.grey900,
  } as TextStyle,
  nameFlexBox: {
    justifyContent: 'space-between',
    alignItems: 'center',
    flexDirection: 'row',
  },
  dividerSpaceBlock: {
    paddingVertical: 4,
    paddingHorizontal: 0,
    justifyContent: 'center',
  },
  text1: {
    textAlign: 'left',
    alignSelf: 'stretch',
    marginBottom: 8,
  },
  divider: {
    alignSelf: 'stretch',
    overflow: 'hidden',
    borderStyle: 'dashed',
    borderColor: colors.grey200,
    borderRadius: 1,
    borderTopWidth: 1,
    borderWidth: 1,
    height: 1,
  },
  nameParent: {
    gap: 16,
    alignSelf: 'stretch',
  },
  text19: {
    fontWeight: '500',
    letterSpacing: -0.1,
    fontSize: 18,
    textAlign: 'left',
    alignSelf: 'flex-start',
  },
  nameContainer: {
    gap: 16,
    alignSelf: 'stretch',
    flexDirection: 'row',
  },
  div: {
    paddingTop: 16,
    gap: 16,
  },
  profileHeader: {
    alignItems: 'center',
    gap: 16,
  },
  profileImage: {
    width: 75,
    height: 75,
    borderRadius: 50,
    backgroundColor: colors.grayishBlue, // fallback background while loading
  },
  profileHeaderText: {
    ...h3,
    fontWeight: '500',
    fontSize: 18,
    marginBottom: 6,
    color: colors.grey900,
  } as TextStyle,
});

export default ProfileScreen;
