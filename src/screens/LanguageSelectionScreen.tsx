import React, { useState } from 'react';
import { ScrollView, View, StyleSheet } from 'react-native';
import Title from '~/components/text/Title';
import * as locales from '~/localization';
import ScreenWrapper from '~/screens/ScreenWrapper';
import colors from '~/styles/colors';
import LanguageOption from '~/components/buttons/LanguageOption';
import { useBottomTabBarHeight } from '@react-navigation/bottom-tabs';

// TODO: Update app language based off of selected language
const LanguageSelectionScreen = () => {
  const [selectedLanguage, setSelectedLanguage] = useState('en');
  const localeEntries = Object.entries(locales) as Array<
    [keyof typeof locales, (typeof locales)[keyof typeof locales]]
  >;

  const bottomTabBarHeight = useBottomTabBarHeight();
  const scrollContainerStyle = {
    paddingBottom: bottomTabBarHeight,
  };

  return (
    <ScreenWrapper color={colors.white}>
      <ScreenWrapper.Body withoutPadding>
        <ScrollView
          contentContainerStyle={scrollContainerStyle}
          showsVerticalScrollIndicator={false}>
          <View style={styles.container}>
            <Title
              title={locales.en.select_language}
              subtitle={locales.en.language_information}
            />

            <View>
              {/* TODO: store flag images locally */}
              {localeEntries.map(([code, L]) => (
                <LanguageOption
                  key={code}
                  languageName={L.language_name}
                  secondaryText={L.language_english}
                  flagUri={L.flagUri}
                  isSelected={selectedLanguage === code}
                  onPress={() => setSelectedLanguage(code)}
                />
              ))}
            </View>
          </View>
        </ScrollView>
      </ScreenWrapper.Body>
    </ScreenWrapper>
  );
};

/* TODO: make sure each card is the same size? */
const styles = StyleSheet.create({
  container: {
    marginTop: 10,
    padding: 16,
    flex: 1,
    backgroundColor: colors.white,
    gap: 12,
  },
});

export default LanguageSelectionScreen;

// TODO: test on different screens and phones
